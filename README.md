# موقع الذِّكر الحكيم 🕌

موقع إسلامي شامل بتصميم زجاجي عصري (Glassmorphism) يهدف إلى تقديم محتوى ديني غني ومتجدد بطريقة عصرية وجذابة.

## 🌟 المميزات

### التصميم
- **تصميم زجاجي عصري (Glassmorphism)** مع تأثيرات شفافية متقدمة
- **دعم كامل للوضع الليلي** مع حفظ التفضيلات
- **تصميم متجاوب** يعمل على جميع الأجهزة والشاشات
- **دعم كامل للغة العربية (RTL)** مع خطوط جميلة
- **تأثيرات تفاعلية سلسة** وانتقالات ناعمة

### المحتوى
- **إحصائيات القرآن الكريم** (604 صفحة، 30 جزء، 114 سورة، 6236 آية)
- **خدمات إسلامية متنوعة** (قراءة القرآن، الأحاديث، الأذكار، المسبحة الإلكترونية)
- **محتوى أصيل وموثوق** من المصادر الإسلامية المعتمدة

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الموقع
- **CSS3** - التصميم والتأثيرات الزجاجية
- **JavaScript (Vanilla)** - التفاعلات والوضع الليلي
- **Bootstrap 5** - التصميم المتجاوب
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية

## 📁 هيكل المشروع

```
alzekr_alhakem/
│
├── index.html          # الصفحة الرئيسية
├── services.html       # صفحة الخدمات
├── about.html          # صفحة حولنا
├── README.md           # ملف التوثيق
│
├── css/
│   └── style.css       # ملف التنسيق الرئيسي
│
├── js/
│   └── script.js       # ملف JavaScript للتفاعلات
│
├── assets/
│   ├── images/         # الصور والشعارات
│   ├── icons/          # الأيقونات
│   └── fonts/          # الخطوط المخصصة
│
└── libs/
    ├── bootstrap/      # ملفات Bootstrap
    └── fontawesome/    # ملفات Font Awesome
```

## 🚀 كيفية التشغيل

### الطريقة الأولى: خادم Python البسيط
```bash
# انتقل إلى مجلد المشروع
cd alzekr_alhakem

# شغل الخادم المحلي
python -m http.server 8000

# افتح المتصفح على
http://localhost:8000
```

### الطريقة الثانية: Live Server (VS Code)
1. افتح المشروع في VS Code
2. قم بتثبيت إضافة "Live Server"
3. انقر بزر الماوس الأيمن على `index.html`
4. اختر "Open with Live Server"

## 🎨 المميزات التقنية

### تأثيرات الزجاج (Glassmorphism)
- استخدام `backdrop-filter: blur()` للتأثير الزجاجي
- شفافية متدرجة مع `rgba()`
- ظلال ناعمة وحدود شفافة
- تأثيرات الهوفر التفاعلية

### الوضع الليلي
- تبديل سلس بين الوضع النهاري والليلي
- حفظ التفضيلات في `localStorage`
- ألوان محسنة للقراءة في الظلام
- تأثيرات انتقال ناعمة

### الأداء والاستجابة
- تحسين للأجهزة ضعيفة الأداء
- تحميل تدريجي للصور
- تقليل التأثيرات على الاتصالات البطيئة
- دعم `prefers-reduced-motion`

## 📱 التوافق

- ✅ Chrome 88+
- ✅ Firefox 94+
- ✅ Safari 14+
- ✅ Edge 88+
- ✅ جميع الأجهزة المحمولة

## 🔧 التخصيص

### تغيير الألوان
يمكنك تعديل الألوان من خلال متغيرات CSS في ملف `style.css`:

```css
:root {
  --islamic-gold: #d4af37;
  --islamic-green: #2e8b57;
  --islamic-blue: #4682b4;
  --glass-bg-light: rgba(255, 255, 255, 0.15);
  --glass-bg-dark: rgba(0, 0, 0, 0.4);
}
```

### إضافة خدمات جديدة
1. افتح ملف `services.html`
2. أضف بطاقة خدمة جديدة باستخدام الهيكل الموجود
3. أضف الأيقونة المناسبة من Font Awesome

## 🤝 المساهمة

نرحب بمساهماتكم لتطوير الموقع:

1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

## 👨‍💻 المطور

**Osama Developer**
- موقع إسلامي متخصص في التقنيات الحديثة
- تطوير مواقع إسلامية بتصميم عصري

## 🙏 شكر وتقدير

- شكر خاص لجميع المساهمين في المحتوى الإسلامي
- تقدير للمجتمع الإسلامي الرقمي
- الحمد لله رب العالمين

---

> "إِنَّ هَٰذَا الْقُرْآنَ يَهْدِي لِلَّتِي هِيَ أَقْوَمُ" - سورة الإسراء

**نُورُكَ في القرآن، فلتبصر به قلبك** ✨
