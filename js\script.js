// ===== موقع الذِّكر الحكيم - تصميم جديد وعصري =====

// تم حذف نظام الجسيمات لتحسين الأداء

// ===== إدارة مؤشر التمرير =====
class ScrollIndicator {
    constructor() {
        this.indicator = document.querySelector('.scroll-indicator');
        this.init();
    }

    init() {
        if (!this.indicator) return;
        window.addEventListener('scroll', () => this.updateIndicator());
    }

    updateIndicator() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = scrollTop / docHeight;
        this.indicator.style.transform = `scaleX(${scrollPercent})`;
    }
}

// ===== تأثيرات التفاعل المتقدمة =====
class InteractionEffects {
    constructor() {
        this.init();
    }

    init() {
        this.setupHoverEffects();
        this.setupClickEffects();
        this.setupParallaxEffect();
    }

    setupHoverEffects() {
        // تأثيرات الهوفر للبطاقات
        document.querySelectorAll('.stats-card, .service-card').forEach(card => {
            card.addEventListener('mouseenter', (e) => this.createHoverEffect(e));
            card.addEventListener('mouseleave', (e) => this.removeHoverEffect(e));
        });
    }

    createHoverEffect(event) {
        const card = event.currentTarget;
        const rect = card.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        card.style.setProperty('--mouse-x', x + 'px');
        card.style.setProperty('--mouse-y', y + 'px');
    }

    removeHoverEffect(event) {
        const card = event.currentTarget;
        card.style.removeProperty('--mouse-x');
        card.style.removeProperty('--mouse-y');
    }

    setupClickEffects() {
        // تأثيرات النقر
        document.querySelectorAll('.btn-service, .stats-card, .service-card').forEach(element => {
            element.addEventListener('click', (e) => this.createClickEffect(e));
        });
    }

    createClickEffect(event) {
        const element = event.currentTarget;
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }

    setupParallaxEffect() {
        // تم تعطيل تأثير المنظور لتحسين الأداء
    }
}

// ===== تأثيرات الظهور التدريجي =====
class AnimationManager {
    constructor() {
        this.observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverEffects();
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, this.observerOptions);

        // مراقبة العناصر التي تحتاج تأثير الظهور
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });
    }

    setupScrollAnimations() {
        // تأثير التمرير السلس للروابط الداخلية
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    setupHoverEffects() {
        // تأثيرات الهوفر للبطاقات
        document.querySelectorAll('.glass-box').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }
}

// ===== إدارة الأداء =====
class PerformanceManager {
    constructor() {
        this.init();
    }

    init() {
        this.optimizeForLowEndDevices();
        this.setupLazyLoading();
    }

    optimizeForLowEndDevices() {
        // تقليل التأثيرات على الأجهزة الضعيفة
        const isLowEndDevice = this.detectLowEndDevice();
        
        if (isLowEndDevice) {
            document.documentElement.style.setProperty('--glass-blur', '8px');
            document.body.classList.add('reduced-motion');
        }
    }

    detectLowEndDevice() {
        // فحص بسيط للأجهزة الضعيفة
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        const slowConnection = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');
        const lowMemory = navigator.deviceMemory && navigator.deviceMemory < 4;
        const oldBrowser = !window.IntersectionObserver;

        return slowConnection || lowMemory || oldBrowser;
    }

    setupLazyLoading() {
        // تحميل الصور بشكل تدريجي
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }
}

// ===== إدارة التفاعلات الخاصة =====
class InteractionManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupStatsAnimation();
        this.setupServiceCards();
        this.setupNavbarEffects();
    }

    setupStatsAnimation() {
        // تأثير العد التصاعدي للإحصائيات
        const animateNumber = (element, target, duration = 2000) => {
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString('ar-SA');
            }, 16);
        };

        // مراقبة ظهور بطاقات الإحصائيات
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const numberElement = entry.target.querySelector('.stats-number');
                    const targetNumber = parseInt(numberElement.dataset.target);
                    animateNumber(numberElement, targetNumber);
                    statsObserver.unobserve(entry.target);
                }
            });
        });

        document.querySelectorAll('.stats-card').forEach(card => {
            statsObserver.observe(card);
        });
    }

    setupServiceCards() {
        // تأثيرات خاصة لبطاقات الخدمات
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('click', function() {
                // تأثير النقر
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // يمكن إضافة وظائف أخرى هنا مثل فتح نافذة أو توجيه
                console.log('تم النقر على خدمة:', this.querySelector('.service-title').textContent);
            });
        });
    }

    setupNavbarEffects() {
        // تأثير شفافية شريط التنقل عند التمرير
        let lastScrollTop = 0;
        const navbar = document.querySelector('.navbar');

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // التمرير لأسفل
                navbar.style.transform = 'translateY(-100%)';
            } else {
                // التمرير لأعلى
                navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });
    }
}

// ===== إدارة الأخطاء =====
class ErrorManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupErrorHandling();
    }

    setupErrorHandling() {
        window.addEventListener('error', (e) => {
            console.error('خطأ في الموقع:', e.error);
            // يمكن إضافة تقرير الأخطاء هنا
        });

        // التعامل مع الأخطاء غير المتوقعة
        window.addEventListener('unhandledrejection', (e) => {
            console.error('خطأ غير معالج:', e.reason);
            e.preventDefault();
        });
    }
}

// ===== تهيئة التطبيق =====
class App {
    constructor() {
        this.themeManager = null;
        this.animationManager = null;
        this.performanceManager = null;
        this.interactionManager = null;
        this.errorManager = null;
        this.init();
    }

    init() {
        // انتظار تحميل DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeManagers());
        } else {
            this.initializeManagers();
        }
    }

    initializeManagers() {
        try {
            this.errorManager = new ErrorManager();
            this.performanceManager = new PerformanceManager();
            this.animationManager = new AnimationManager();
            this.interactionManager = new InteractionManager();
            // تم حذف نظام الجسيمات
            this.scrollIndicator = new ScrollIndicator();
            this.interactionEffects = new InteractionEffects();

            console.log('تم تحميل موقع الذِّكر الحكيم بنجاح مع التصميم الجديد');
        } catch (error) {
            console.error('خطأ في تهيئة الموقع:', error);
        }
    }
}

// ===== بدء التطبيق =====
const app = new App();

// ===== وظائف مساعدة =====
const utils = {
    // تنسيق الأرقام العربية
    formatArabicNumber: (num) => {
        return num.toLocaleString('ar-SA');
    },

    // فحص دعم المتصفح للتأثيرات
    supportsBackdropFilter: () => {
        return CSS.supports('backdrop-filter', 'blur(1px)') || 
               CSS.supports('-webkit-backdrop-filter', 'blur(1px)');
    },

    // إضافة تأثير التحميل
    showLoading: () => {
        document.body.classList.add('loading');
    },

    hideLoading: () => {
        document.body.classList.remove('loading');
    }
};

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { App, utils };
}
