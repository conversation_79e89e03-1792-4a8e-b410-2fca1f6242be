/* ===== تصميم جديد وعصري - الذِّكر الحكيم ===== */

/* استيراد الخطوط الحديثة */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Amiri:wght@400;700&family=Tajawal:wght@300;400;500;700&display=swap');

/* متغيرات التصميم الجديد */
:root {
  /* ألوان أساسية عصرية */
  --primary-bg: #0f0f23;
  --secondary-bg: #1a1a2e;
  --accent-bg: #16213e;
  --surface-bg: #0f3460;
  
  /* ألوان إسلامية محدثة */
  --islamic-gold: #ffd700;
  --islamic-emerald: #50c878;
  --islamic-sapphire: #0f52ba;
  --islamic-pearl: #f8f6f0;
  --islamic-coral: #ff6b6b;
  
  /* ألوان النص */
  --text-primary: #ffffff;
  --text-secondary: #b8c5d1;
  --text-muted: #8892b0;
  --text-accent: var(--islamic-gold);
  
  /* تدرجات جميلة */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-islamic: linear-gradient(135deg, var(--islamic-emerald) 0%, var(--islamic-sapphire) 100%);
  --gradient-gold: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  
  /* ظلال Neumorphism */
  --shadow-light: 20px 20px 60px #0d0d1f, -20px -20px 60px #111127;
  --shadow-inset: inset 20px 20px 60px #0d0d1f, inset -20px -20px 60px #111127;
  --shadow-hover: 25px 25px 75px #0a0a1a, -25px -25px 75px #14142c;
  
  /* انتقالات محسنة للأداء */
  --transition-smooth: all 0.25s ease;
  --transition-bounce: all 0.3s ease;
  --transition-fast: all 0.15s ease;
  
  /* أحجام ومسافات */
  --border-radius: 20px;
  --border-radius-large: 30px;
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
}

/* ===== إعدادات أساسية جديدة ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Tajawal', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  direction: rtl;
  text-align: right;
  background: var(--primary-bg);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* خلفية بسيطة وسريعة */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(80, 200, 120, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
  z-index: -1;
}

/* ===== شريط التنقل العصري ===== */
.navbar {
  background: rgba(15, 15, 35, 0.95) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-sm) 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: var(--transition-smooth);
  position: relative;
  z-index: 1000;
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-gold);
}

.navbar-brand {
  font-family: 'Amiri', serif;
  font-size: 2rem;
  font-weight: 700;
  color: var(--islamic-gold) !important;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition-smooth);
}

.navbar-brand:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.5));
}

.brand-subtitle {
  display: block;
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--text-secondary);
  margin-top: -5px;
  font-family: 'Tajawal', sans-serif;
}

.navbar-nav .nav-link {
  color: var(--text-primary) !important;
  font-weight: 500;
  margin: 0 var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  transition: var(--transition-smooth);
  position: relative;
  border-bottom: 2px solid transparent;
}

.navbar-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--islamic-gold);
  transition: var(--transition-smooth);
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
  width: 80%;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--islamic-gold) !important;
}

/* ===== العناصر الأساسية الجديدة ===== */
.neumorphic {
  background: var(--secondary-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.neumorphic:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-5px);
}

.neumorphic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
}

/* ===== المحتوى الرئيسي المبسط ===== */
.main-content {
  padding-top: 100px;
  padding-bottom: var(--spacing-lg);
}

.hero-simple {
  text-align: center;
  margin-bottom: var(--spacing-md);
}

.site-title {
  font-family: 'Amiri', serif;
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.welcome-message {
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  color: var(--text-secondary);
  margin-bottom: 0;
  line-height: 1.4;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.stats-compact {
  margin-bottom: var(--spacing-md);
}



/* ===== بطاقات الإحصائيات المحسنة ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-sm);
  margin: 0;
}

.stats-card {
  background: var(--secondary-bg);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-islamic);
  transform: scaleX(0);
  transition: var(--transition-smooth);
}

.stats-card:hover::before {
  transform: scaleX(1);
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
}

.stats-icon {
  font-size: 2.5rem;
  color: var(--islamic-gold);
  margin-bottom: var(--spacing-xs);
  transition: var(--transition-smooth);
}

.stats-card:hover .stats-icon {
  transform: scale(1.05);
  color: var(--islamic-emerald);
}

.stats-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-family: 'Inter', sans-serif;
}

.stats-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-accent);
  margin-bottom: 0;
}

/* ===== الآية القرآنية المحسنة ===== */
.verse-card {
  background: var(--secondary-bg);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  text-align: center;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 215, 0, 0.2);
  max-width: 600px;
  margin: 0 auto;
}

.verse-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-gold);
}

.verse-text {
  font-family: 'Amiri', serif;
  font-size: clamp(1.1rem, 2vw, 1.4rem);
  font-weight: 600;
  color: var(--islamic-emerald);
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
}

.verse-reference {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-style: italic;
  margin-bottom: 0;
}

/* ===== بطاقات الخدمات الجديدة ===== */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-md);
  margin: 0;
}

.service-card {
  background: var(--secondary-bg);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-islamic);
  transform: scaleX(0);
  transition: var(--transition-smooth);
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
  border-color: rgba(255, 215, 0, 0.3);
}

.service-icon {
  font-size: 3rem;
  color: var(--islamic-emerald);
  margin-bottom: var(--spacing-md);
  transition: var(--transition-bounce);
  filter: drop-shadow(0 0 20px rgba(80, 200, 120, 0.3));
}

.service-card:hover .service-icon {
  transform: scale(1.1);
  color: var(--islamic-gold);
}

.service-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.service-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
  font-size: 0.95rem;
}

/* ===== أزرار الخدمات العصرية ===== */
.btn-service {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--gradient-islamic);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius-large);
  font-weight: 600;
  font-size: 0.9rem;
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(80, 200, 120, 0.3);
}

.btn-service::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-gold);
  transition: var(--transition-smooth);
  z-index: -1;
}

.btn-service:hover::before {
  left: 0;
}

.btn-service:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
  color: white;
}

.btn-service i {
  transition: var(--transition-smooth);
}

.btn-service:hover i {
  transform: translateX(-5px);
}

/* ===== التذييل العصري ===== */
.footer {
  background: var(--secondary-bg);
  padding: var(--spacing-xl) 0 var(--spacing-lg);
  margin-top: var(--spacing-xl);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-gold);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.footer-section h4 {
  color: var(--islamic-gold);
  font-size: 1.2rem;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-smooth);
  padding: var(--spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.footer-links a:hover {
  color: var(--islamic-gold);
  transform: translateX(10px);
}

.footer-social {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.footer-social a {
  width: 40px;
  height: 40px;
  background: var(--accent-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: var(--transition-smooth);
  text-decoration: none;
}

.footer-social a:hover {
  background: var(--islamic-gold);
  color: var(--primary-bg);
  transform: translateY(-3px);
}

.footer-info {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-brand {
  font-family: 'Amiri', serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--islamic-gold);
  margin-bottom: var(--spacing-sm);
}

.footer-quote {
  font-family: 'Amiri', serif;
  font-style: italic;
  color: var(--islamic-emerald);
  font-size: 1.1rem;
  margin: var(--spacing-sm) 0;
}

.footer-text {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin: var(--spacing-xs) 0;
}

/* ===== تأثيرات الأنيميشن والتفاعل ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: var(--transition-smooth);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.welcome-text {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

/* ===== تأثيرات الهوفر المتقدمة ===== */
.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background: var(--gradient-gold);
  opacity: 0;
  z-index: -1;
  transition: var(--transition-smooth);
  filter: blur(20px);
}

.glow-effect:hover::after {
  opacity: 0.3;
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 768px) {
  .main-content {
    padding-top: 80px;
  }

  .site-title {
    font-size: 2.5rem;
  }

  .controls-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .reading-controls {
    justify-content: center;
  }

  .sura-grid {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: var(--spacing-xs);
  }

  .sura-icon {
    width: 70px;
    height: 70px;
  }

  .sura-number {
    font-size: 1rem;
  }

  .sura-name {
    font-size: 0.6rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }

  .stats-card {
    padding: var(--spacing-sm);
  }

  .stats-icon {
    font-size: 2rem;
  }

  .stats-number {
    font-size: 1.5rem;
  }

  .verse-card {
    padding: var(--spacing-md);
  }

  .verse-text {
    font-size: 1.2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .navbar-brand {
    font-size: 1.5rem;
  }

  .brand-subtitle {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding-top: 70px;
  }

  .hero-simple {
    margin-bottom: var(--spacing-sm);
  }

  .sura-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 8px;
    padding: var(--spacing-sm);
  }

  .sura-icon {
    width: 60px;
    height: 60px;
  }

  .sura-number {
    font-size: 0.9rem;
  }

  .sura-name {
    font-size: 0.5rem;
  }



  .stats-compact {
    margin-bottom: var(--spacing-sm);
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-xs);
  }

  .stats-card {
    padding: var(--spacing-sm);
  }

  .verse-card {
    padding: var(--spacing-sm);
  }

  .verse-text {
    font-size: 1rem;
  }

  .welcome-message {
    font-size: 0.9rem;
  }
}

/* ===== صفحة قراءة القرآن الكريم ===== */
.sura-selector {
  margin-bottom: var(--spacing-lg);
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
}

.controls-header h3 {
  color: var(--islamic-emerald);
  margin-bottom: 0;
  font-family: 'Amiri', serif;
}

.reading-controls {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}



.sura-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
}

.sura-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--primary-bg);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition-smooth);
  border: 2px solid rgba(255, 215, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.sura-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-islamic);
  opacity: 0;
  transition: var(--transition-smooth);
  border-radius: 50%;
}

.sura-icon:hover::before {
  opacity: 0.1;
}

.sura-icon:hover {
  transform: translateY(-5px) scale(1.05);
  border-color: var(--islamic-gold);
  box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.sura-icon.active {
  background: var(--islamic-gold);
  border-color: var(--islamic-emerald);
  transform: scale(1.1);
}

.sura-icon.active .sura-number {
  color: var(--primary-bg);
}

.sura-icon.active .sura-name {
  color: var(--primary-bg);
}

.sura-number {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--islamic-gold);
  margin-bottom: 2px;
  position: relative;
  z-index: 2;
}

.sura-name {
  font-size: 0.7rem;
  color: var(--text-secondary);
  text-align: center;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.form-label {
  color: var(--text-accent);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.form-select {
  background-color: var(--primary-bg);
  border: 1px solid rgba(255, 215, 0, 0.3);
  color: var(--text-primary);
  border-radius: var(--border-radius);
}

.form-select:focus {
  border-color: var(--islamic-gold);
  box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
  background-color: var(--primary-bg);
  color: var(--text-primary);
}

.reading-options {
  display: flex;
  gap: var(--spacing-xs);
}

.quran-reader {
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.sura-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid var(--islamic-gold);
}

.sura-name {
  font-family: 'Amiri', serif;
  font-size: 2rem;
  color: var(--islamic-emerald);
  margin-bottom: var(--spacing-xs);
}

.sura-details {
  color: var(--text-secondary);
  margin-bottom: 0;
}

.sura-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--islamic-gold);
  background: rgba(255, 215, 0, 0.1);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--islamic-gold);
}

.bismillah {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: rgba(80, 200, 120, 0.1);
  border-radius: var(--border-radius);
  border: 1px solid rgba(80, 200, 120, 0.3);
}

.bismillah .arabic-text {
  font-family: 'Amiri', serif;
  font-size: 1.5rem;
  color: var(--islamic-emerald);
  margin-bottom: 0;
  font-weight: 700;
}

.verses-container {
  margin-bottom: var(--spacing-lg);
}

.verse {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  transition: var(--transition-smooth);
  cursor: pointer;
}

.verse:hover {
  background: rgba(255, 215, 0, 0.05);
  transform: translateX(-5px);
}

.verse-number {
  background: var(--islamic-gold);
  color: var(--primary-bg);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
  margin-left: var(--spacing-sm);
  flex-shrink: 0;
}

.verse-text {
  font-family: 'Amiri', serif;
  font-size: 1.4rem;
  line-height: 2;
  color: var(--text-primary);
  text-align: justify;
}



.navigation-controls {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 215, 0, 0.3);
}



/* ===== تحسينات الأداء ===== */
.stats-card,
.service-card,
.neumorphic {
  will-change: transform;
  backface-visibility: hidden;
}

/* ===== تأثيرات التمرير ===== */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--gradient-gold);
  transform-origin: left;
  transform: scaleX(0);
  z-index: 9999;
  transition: transform 0.1s ease-out;
}

/* ===== تحسينات إضافية ===== */
.section-title {
  font-family: 'Amiri', serif;
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  text-align: center;
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--spacing-lg);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* تم حذف تأثيرات الجسيمات لتحسين الأداء */
