/* ===== تصميم جديد وعصري - الذِّكر الحكيم ===== */

/* استيراد الخطوط الحديثة */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Amiri:wght@400;700&family=Tajawal:wght@300;400;500;700&display=swap');

/* متغيرات التصميم الجديد */
:root {
  /* ألوان أساسية عصرية */
  --primary-bg: #0f0f23;
  --secondary-bg: #1a1a2e;
  --accent-bg: #16213e;
  --surface-bg: #0f3460;
  
  /* ألوان إسلامية محدثة */
  --islamic-gold: #ffd700;
  --islamic-emerald: #50c878;
  --islamic-sapphire: #0f52ba;
  --islamic-pearl: #f8f6f0;
  --islamic-coral: #ff6b6b;
  
  /* ألوان النص */
  --text-primary: #ffffff;
  --text-secondary: #b8c5d1;
  --text-muted: #8892b0;
  --text-accent: var(--islamic-gold);
  
  /* تدرجات جميلة */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-islamic: linear-gradient(135deg, var(--islamic-emerald) 0%, var(--islamic-sapphire) 100%);
  --gradient-gold: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  
  /* ظلال Neumorphism */
  --shadow-light: 20px 20px 60px #0d0d1f, -20px -20px 60px #111127;
  --shadow-inset: inset 20px 20px 60px #0d0d1f, inset -20px -20px 60px #111127;
  --shadow-hover: 25px 25px 75px #0a0a1a, -25px -25px 75px #14142c;
  
  /* انتقالات سلسة */
  --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-fast: all 0.2s ease-out;
  
  /* أحجام ومسافات */
  --border-radius: 20px;
  --border-radius-large: 30px;
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
}

/* ===== إعدادات أساسية جديدة ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Tajawal', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  direction: rtl;
  text-align: right;
  background: var(--primary-bg);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* خلفية متحركة جميلة */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(80, 200, 120, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(15, 82, 186, 0.1) 0%, transparent 50%);
  z-index: -1;
  animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
  0%, 100% { transform: translateX(0) translateY(0); }
  33% { transform: translateX(-20px) translateY(-20px); }
  66% { transform: translateX(20px) translateY(-10px); }
}

/* ===== شريط التنقل العصري ===== */
.navbar {
  background: rgba(15, 15, 35, 0.95) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-sm) 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: var(--transition-smooth);
  position: relative;
  z-index: 1000;
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-gold);
}

.navbar-brand {
  font-family: 'Amiri', serif;
  font-size: 2rem;
  font-weight: 700;
  color: var(--islamic-gold) !important;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition-smooth);
}

.navbar-brand:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.5));
}

.brand-subtitle {
  display: block;
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--text-secondary);
  margin-top: -5px;
  font-family: 'Tajawal', sans-serif;
}

.navbar-nav .nav-link {
  color: var(--text-primary) !important;
  font-weight: 500;
  margin: 0 var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.navbar-nav .nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-islamic);
  transition: var(--transition-smooth);
  z-index: -1;
}

.navbar-nav .nav-link:hover::before {
  left: 0;
}

.navbar-nav .nav-link:hover {
  color: white !important;
  transform: translateY(-2px);
}

/* ===== العناصر الأساسية الجديدة ===== */
.neumorphic {
  background: var(--secondary-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.neumorphic:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-5px);
}

.neumorphic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
}

/* ===== القسم الرئيسي ===== */
.hero-section {
  padding: var(--spacing-xl) 0;
  text-align: center;
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-family: 'Amiri', serif;
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.hero-subtitle {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* ===== بطاقات الإحصائيات الجديدة ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.stats-card {
  background: var(--secondary-bg);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-islamic);
  transform: scaleX(0);
  transition: var(--transition-smooth);
}

.stats-card:hover::before {
  transform: scaleX(1);
}

.stats-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-hover);
}

.stats-icon {
  font-size: 3rem;
  color: var(--islamic-gold);
  margin-bottom: var(--spacing-sm);
  transition: var(--transition-bounce);
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));
}

.stats-card:hover .stats-icon {
  transform: scale(1.2) rotate(5deg);
  color: var(--islamic-emerald);
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-family: 'Inter', sans-serif;
}

.stats-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-accent);
  margin-bottom: var(--spacing-xs);
}

.stats-description {
  font-size: 0.9rem;
  color: var(--text-muted);
  line-height: 1.4;
}

/* ===== الآية القرآنية ===== */
.quran-verse {
  padding: var(--spacing-xl) 0;
}

.verse-card {
  background: var(--secondary-bg);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.verse-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-gold);
}

.verse-text {
  font-family: 'Amiri', serif;
  font-size: clamp(1.5rem, 3vw, 2.2rem);
  font-weight: 700;
  color: var(--islamic-emerald);
  margin-bottom: var(--spacing-md);
  line-height: 1.4;
  text-shadow: 0 0 20px rgba(80, 200, 120, 0.3);
}

.verse-reference {
  color: var(--text-secondary);
  font-size: 1.1rem;
  font-style: italic;
}

/* ===== بطاقات الخدمات الجديدة ===== */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.service-card {
  background: var(--secondary-bg);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-islamic);
  transform: scaleX(0);
  transition: var(--transition-smooth);
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-hover);
  border-color: rgba(255, 215, 0, 0.3);
}

.service-icon {
  font-size: 3rem;
  color: var(--islamic-emerald);
  margin-bottom: var(--spacing-md);
  transition: var(--transition-bounce);
  filter: drop-shadow(0 0 20px rgba(80, 200, 120, 0.3));
}

.service-card:hover .service-icon {
  transform: scale(1.2) rotate(-5deg);
  color: var(--islamic-gold);
  filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.5));
}

.service-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.service-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
  font-size: 0.95rem;
}

/* ===== أزرار الخدمات العصرية ===== */
.btn-service {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--gradient-islamic);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius-large);
  font-weight: 600;
  font-size: 0.9rem;
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(80, 200, 120, 0.3);
}

.btn-service::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-gold);
  transition: var(--transition-smooth);
  z-index: -1;
}

.btn-service:hover::before {
  left: 0;
}

.btn-service:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
  color: white;
}

.btn-service i {
  transition: var(--transition-smooth);
}

.btn-service:hover i {
  transform: translateX(-5px);
}

/* ===== التذييل العصري ===== */
.footer {
  background: var(--secondary-bg);
  padding: var(--spacing-xl) 0 var(--spacing-lg);
  margin-top: var(--spacing-xl);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-gold);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.footer-section h4 {
  color: var(--islamic-gold);
  font-size: 1.2rem;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-smooth);
  padding: var(--spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.footer-links a:hover {
  color: var(--islamic-gold);
  transform: translateX(10px);
}

.footer-social {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.footer-social a {
  width: 40px;
  height: 40px;
  background: var(--accent-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: var(--transition-smooth);
  text-decoration: none;
}

.footer-social a:hover {
  background: var(--islamic-gold);
  color: var(--primary-bg);
  transform: translateY(-3px);
}

.footer-info {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-brand {
  font-family: 'Amiri', serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--islamic-gold);
  margin-bottom: var(--spacing-sm);
}

.footer-quote {
  font-family: 'Amiri', serif;
  font-style: italic;
  color: var(--islamic-emerald);
  font-size: 1.1rem;
  margin: var(--spacing-sm) 0;
}

.footer-text {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin: var(--spacing-xs) 0;
}

/* ===== تأثيرات الأنيميشن والتفاعل ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: var(--transition-smooth);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.welcome-text {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

/* ===== تأثيرات الهوفر المتقدمة ===== */
.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background: var(--gradient-gold);
  opacity: 0;
  z-index: -1;
  transition: var(--transition-smooth);
  filter: blur(20px);
}

.glow-effect:hover::after {
  opacity: 0.3;
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .navbar-brand {
    font-size: 1.5rem;
  }

  .brand-subtitle {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: var(--spacing-lg) 0;
  }

  .stats-card,
  .service-card {
    padding: var(--spacing-md);
  }

  .verse-card {
    padding: var(--spacing-lg);
  }

  .verse-text {
    font-size: 1.3rem;
  }
}

/* ===== تأثيرات خاصة ===== */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* ===== تحسينات الأداء ===== */
.stats-card,
.service-card,
.neumorphic {
  will-change: transform;
  backface-visibility: hidden;
}

/* ===== تأثيرات التمرير ===== */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--gradient-gold);
  transform-origin: left;
  transform: scaleX(0);
  z-index: 9999;
  transition: transform 0.1s ease-out;
}

/* ===== تحسينات إضافية ===== */
.section-title {
  font-family: 'Amiri', serif;
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  text-align: center;
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--spacing-lg);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* ===== تأثيرات الجسيمات ===== */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--islamic-gold);
  border-radius: 50%;
  animation: particleFloat 15s infinite linear;
  opacity: 0.3;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
  }
  90% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(-100px) translateX(100px);
    opacity: 0;
  }
}
