/* ===== متغيرات CSS للتحكم في التصميم ===== */
:root {
  /* ألوان الزجاج */
  --glass-bg-light: rgba(255, 255, 255, 0.15);
  --glass-bg-dark: rgba(0, 0, 0, 0.4);
  --glass-border-light: rgba(255, 255, 255, 0.2);
  --glass-border-dark: rgba(255, 255, 255, 0.1);
  
  /* تأثيرات الزجاج */
  --glass-blur: 16px;
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  --glass-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.3);
  
  /* ألوان النص */
  --text-primary-light: #2c3e50;
  --text-secondary-light: #5a6c7d;
  --text-primary-dark: #ecf0f1;
  --text-secondary-dark: #bdc3c7;
  
  /* ألوان الخلفية */
  --bg-gradient-light: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  
  /* ألوان إسلامية */
  --islamic-gold: #d4af37;
  --islamic-green: #2e8b57;
  --islamic-blue: #4682b4;
}

/* ===== إعدادات عامة ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;
  background: var(--bg-gradient-dark);
  background-attachment: fixed;
  color: var(--text-primary-dark);
  transition: all 0.3s ease;
  min-height: 100vh;
  overflow-x: hidden;
}

/* ===== فئة الزجاج الأساسية ===== */
.glass-box {
  background: var(--glass-bg-dark);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border-dark);
  border-radius: 20px;
  box-shadow: var(--glass-shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.glass-box:hover {
  transform: translateY(-5px);
  box-shadow: var(--glass-shadow-hover);
}

.glass-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
}

/* ===== شريط التنقل ===== */
.navbar {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.3) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 0;
}

.navbar-brand {
  font-size: 2.5rem;
  font-weight: 900;
  color: var(--islamic-gold) !important;
  text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
  font-family: 'Amiri', 'Cairo', serif;
  letter-spacing: 2px;
}

.navbar-brand .brand-subtitle {
  display: block;
  font-size: 0.8rem;
  font-weight: 400;
  color: var(--text-secondary-dark);
  margin-top: -5px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.navbar-nav .nav-link {
  color: var(--text-primary-dark) !important;
  font-weight: 500;
  margin: 0 10px;
  transition: all 0.3s ease;
  position: relative;
}

.navbar-nav .nav-link:hover {
  color: var(--islamic-gold) !important;
  transform: translateY(-2px);
}

.navbar-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--islamic-gold);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
  width: 100%;
}



/* ===== العنوان الرئيسي ===== */
.hero-section {
  padding: 100px 0 50px;
  text-align: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  background: linear-gradient(45deg, var(--islamic-gold), var(--islamic-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.hero-subtitle {
  font-size: 1.3rem;
  color: var(--text-secondary-light);
  margin-bottom: 40px;
  line-height: 1.6;
}

.dark-mode .hero-subtitle {
  color: var(--text-secondary-dark);
}

/* ===== الآية القرآنية ===== */
.verse-text {
  font-size: 2.2rem;
  font-weight: 600;
  color: var(--islamic-green);
  line-height: 1.4;
  font-family: 'Amiri', serif;
  margin-bottom: 20px;
}

.verse-reference {
  color: var(--text-secondary-dark);
  font-style: italic;
  font-size: 1.1rem;
}

.welcome-text {
  font-size: 1.2rem;
  color: var(--text-secondary-dark);
  margin-bottom: 40px;
  line-height: 1.6;
}

/* ===== بطاقات الإحصائيات المحسنة ===== */
.stats-card {
  padding: 35px 25px;
  text-align: center;
  margin-bottom: 30px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border-radius: 25px;
}

.stats-icon {
  font-size: 2.5rem;
  color: var(--islamic-gold);
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.stats-card:hover .stats-icon {
  transform: scale(1.1);
  color: var(--islamic-green);
}

.stats-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.stats-card:hover::after {
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
}

.stats-number {
  font-size: 3.5rem;
  font-weight: bold;
  color: var(--islamic-gold);
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
}

.stats-card:hover .stats-number {
  transform: scale(1.05);
  color: var(--islamic-green);
}

.stats-label {
  font-size: 1.3rem;
  color: var(--text-primary-dark);
  font-weight: 600;
  margin-bottom: 8px;
}

.stats-description {
  font-size: 0.9rem;
  color: var(--text-secondary-dark);
  font-style: italic;
  opacity: 0.8;
}

/* ===== بطاقات الخدمات المحسنة والأنيقة ===== */
.service-card {
  padding: 20px 18px;
  text-align: center;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 18px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--islamic-gold), var(--islamic-green));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover::before {
  opacity: 1;
}

.service-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 12px 30px rgba(0,0,0,0.4);
}

.service-icon {
  font-size: 2.2rem;
  color: var(--islamic-green);
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.15);
  color: var(--islamic-gold);
}

.service-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--text-primary-dark);
  line-height: 1.3;
}

.service-description {
  color: var(--text-secondary-dark);
  line-height: 1.4;
  font-size: 0.9rem;
  flex-grow: 1;
  margin-bottom: 15px;
}

/* ===== أزرار الخدمات الأنيقة ===== */
.service-button {
  margin-top: auto;
}

.btn-service {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background: linear-gradient(45deg, var(--islamic-green), var(--islamic-blue));
  color: white;
  text-decoration: none;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.btn-service::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--islamic-gold), var(--islamic-green));
  transition: left 0.3s ease;
  z-index: -1;
}

.btn-service:hover::before {
  left: 0;
}

.btn-service:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.btn-service i {
  transition: transform 0.3s ease;
}

.btn-service:hover i {
  transform: translateX(-3px);
}

/* ===== التذييل المحسن ===== */
.footer {
  margin-top: 80px;
  padding: 60px 0 30px;
  background: var(--glass-bg-dark);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border-top: 1px solid var(--glass-border-dark);
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--islamic-gold), transparent);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  color: var(--islamic-gold);
  font-size: 1.3rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-links a {
  color: var(--text-primary-dark);
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 5px 0;
  border-bottom: 1px solid transparent;
}

.footer-links a:hover {
  color: var(--islamic-gold);
  border-bottom-color: var(--islamic-gold);
  transform: translateX(10px);
}

.footer-info {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-brand {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--islamic-gold);
  margin-bottom: 10px;
  font-family: 'Amiri', serif;
}

.footer-text {
  color: var(--text-secondary-dark);
  margin: 8px 0;
  font-size: 0.95rem;
}

.footer-quote {
  font-style: italic;
  color: var(--islamic-green);
  font-size: 1.1rem;
  margin: 20px 0;
  font-family: 'Amiri', serif;
}

.footer-social {
  margin: 20px 0;
}

.footer-social a {
  color: var(--text-secondary-dark);
  font-size: 1.5rem;
  margin: 0 10px;
  transition: all 0.3s ease;
}

.footer-social a:hover {
  color: var(--islamic-gold);
  transform: translateY(-3px);
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .stats-number {
    font-size: 2.8rem;
  }

  .navbar-brand {
    font-size: 2rem;
  }

  .navbar-brand .brand-subtitle {
    font-size: 0.7rem;
  }

  .glass-box {
    margin: 10px 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .verse-text {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .stats-card {
    padding: 18px 12px;
  }

  .service-card {
    padding: 18px 15px;
  }

  .service-icon {
    font-size: 2rem;
  }

  .service-title {
    font-size: 1.1rem;
  }

  .btn-service {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

/* ===== تحسينات الأداء ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== تأثيرات إضافية ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* ===== تحسينات إضافية للصفحات ===== */
.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  background: linear-gradient(45deg, var(--islamic-gold), var(--islamic-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary-light);
  margin-bottom: 40px;
  line-height: 1.6;
}

.dark-mode .section-subtitle {
  color: var(--text-secondary-dark);
}

.quote-text {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--islamic-green);
  line-height: 1.4;
  font-family: 'Amiri', serif;
}

.quote-reference {
  color: var(--text-secondary-light);
  font-style: italic;
}

.dark-mode .quote-reference {
  color: var(--text-secondary-dark);
}

/* ===== تأثيرات خاصة للخدمات ===== */
.service-features {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 15px;
}

.about-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
}

/* ===== تحسينات الاستجابة للأجهزة الصغيرة ===== */
@media (max-width: 480px) {
  .section-title {
    font-size: 2rem;
  }

  .quote-text {
    font-size: 1.4rem;
  }

  .service-card {
    padding: 25px 20px;
  }

  .service-icon {
    font-size: 2.5rem;
  }
}

/* ===== تأثيرات التحميل ===== */
.loading {
  overflow: hidden;
}

.loading::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-gradient-light);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark-mode.loading::before {
  background: var(--bg-gradient-dark);
}

/* ===== تحسينات إضافية للأداء ===== */
.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* ===== تأثيرات خاصة للنصوص ===== */
.text-glow {
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
}

.icon-glow {
  filter: drop-shadow(0 0 8px rgba(212, 175, 55, 0.3));
}

/* ===== تحسينات إضافية للجمالية ===== */
.navbar {
  box-shadow: 0 2px 20px rgba(0,0,0,0.3);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--islamic-gold), var(--islamic-green), var(--islamic-blue));
  border-radius: 25px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:hover::before {
  opacity: 0.3;
}

.service-card::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, var(--islamic-green), var(--islamic-gold));
  border-radius: 20px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover::before {
  opacity: 0.2;
}

/* تحسين الخطوط */
.hero-title, .section-title {
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.verse-text {
  text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
}

/* تحسين الانتقالات */
* {
  scroll-behavior: smooth;
}

.glass-box {
  will-change: transform;
}

/* تحسين الأداء */
.stats-card, .service-card {
  transform: translateZ(0);
  backface-visibility: hidden;
}
