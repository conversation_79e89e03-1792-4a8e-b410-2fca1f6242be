// ===== صفحة قراءة القرآن الكريم =====

class QuranReader {
    constructor() {
        this.currentSura = 1;
        this.nightMode = false;
        
        this.suras = {
            1: { name: 'الفاتحة', type: 'مكية', verses: 7 },
            2: { name: 'البقرة', type: 'مدنية', verses: 286 },
            3: { name: 'آل عمران', type: 'مدنية', verses: 200 },
            4: { name: 'النساء', type: 'مدنية', verses: 176 },
            5: { name: 'المائدة', type: 'مدنية', verses: 120 },
            18: { name: 'الكهف', type: 'مكية', verses: 110 },
            36: { name: 'يس', type: 'مكية', verses: 83 },
            67: { name: 'الملك', type: 'مكية', verses: 30 },
            112: { name: 'الإخلاص', type: 'مكية', verses: 4 },
            113: { name: 'الفلق', type: 'مكية', verses: 5 },
            114: { name: 'الناس', type: 'مكية', verses: 6 }
        };
        
        this.verses = {
            1: [
                'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
                'الرَّحْمَٰنِ الرَّحِيمِ',
                'مَالِكِ يَوْمِ الدِّينِ',
                'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ',
                'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ',
                'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ'
            ],
            112: [
                'قُلْ هُوَ اللَّهُ أَحَدٌ',
                'اللَّهُ الصَّمَدُ',
                'لَمْ يَلِدْ وَلَمْ يُولَدْ',
                'وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ'
            ],
            113: [
                'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ',
                'مِن شَرِّ مَا خَلَقَ',
                'وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ',
                'وَمِن شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ',
                'وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ'
            ],
            114: [
                'قُلْ أَعُوذُ بِرَبِّ النَّاسِ',
                'مَلِكِ النَّاسِ',
                'إِلَٰهِ النَّاسِ',
                'مِن شَرِّ الْوَسْوَاسِ الْخَنَّاسِ',
                'الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ',
                'مِنَ الْجِنَّةِ وَالنَّاسِ'
            ]
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadSura(this.currentSura);
        this.setActiveSura(this.currentSura);
    }
    
    setupEventListeners() {
        // النقر على أيقونات السور
        document.querySelectorAll('.sura-icon').forEach(icon => {
            icon.addEventListener('click', (e) => {
                const suraNumber = parseInt(e.currentTarget.getAttribute('data-sura'));
                this.loadSura(suraNumber);
                this.setActiveSura(suraNumber);
            });
        });

        // الوضع الليلي
        document.getElementById('nightModeBtn').addEventListener('click', () => {
            this.toggleNightMode();
        });

        // التنقل
        document.getElementById('prevBtn')?.addEventListener('click', () => {
            this.navigateSura(-1);
        });

        document.getElementById('nextBtn')?.addEventListener('click', () => {
            this.navigateSura(1);
        });

        // النقر على الآيات
        document.addEventListener('click', (e) => {
            if (e.target.closest('.verse')) {
                this.highlightVerse(e.target.closest('.verse'));
            }
        });
    }
    
    loadSura(suraNumber) {
        this.currentSura = suraNumber;
        const sura = this.suras[suraNumber];
        const verses = this.verses[suraNumber] || [];
        
        // تحديث معلومات السورة
        document.querySelector('.sura-name').textContent = `سورة ${sura.name}`;
        document.querySelector('.sura-details').textContent = `${sura.type} • ${sura.verses} آيات`;
        document.querySelector('.sura-number').textContent = suraNumber;
        
        // تحديث الآيات
        const container = document.getElementById('versesContainer');
        container.innerHTML = '';
        
        verses.forEach((verseText, index) => {
            const verseDiv = document.createElement('div');
            verseDiv.className = 'verse';
            verseDiv.setAttribute('data-verse', index + 1);
            verseDiv.innerHTML = `
                <span class="verse-number">${index + 1}</span>
                <span class="verse-text">${verseText}</span>
            `;
            container.appendChild(verseDiv);
        });
    }
    
    setActiveSura(suraNumber) {
        // إزالة التحديد من جميع الأيقونات
        document.querySelectorAll('.sura-icon').forEach(icon => {
            icon.classList.remove('active');
        });

        // تحديد الأيقونة النشطة
        const activeIcon = document.querySelector(`[data-sura="${suraNumber}"]`);
        if (activeIcon) {
            activeIcon.classList.add('active');
        }
    }

    toggleNightMode() {
        this.nightMode = !this.nightMode;
        document.body.classList.toggle('night-mode', this.nightMode);

        const btn = document.getElementById('nightModeBtn');
        btn.innerHTML = this.nightMode ?
            '<i class="fas fa-sun"></i> الوضع النهاري' :
            '<i class="fas fa-moon"></i> الوضع الليلي';
    }
    
    navigateSura(direction) {
        const suraNumbers = Object.keys(this.suras).map(Number).sort((a, b) => a - b);
        const currentIndex = suraNumbers.indexOf(this.currentSura);
        const newIndex = currentIndex + direction;

        if (newIndex >= 0 && newIndex < suraNumbers.length) {
            const newSura = suraNumbers[newIndex];
            this.loadSura(newSura);
            this.setActiveSura(newSura);
        }
    }
    
    highlightVerse(verseElement) {
        // إزالة التمييز من الآيات الأخرى
        document.querySelectorAll('.verse').forEach(v => v.classList.remove('highlighted'));

        // تمييز الآية المحددة
        verseElement.classList.add('highlighted');
    }
}

// تشغيل قارئ القرآن عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new QuranReader();
});

// إضافة CSS للتمييز والإشعارات
const style = document.createElement('style');
style.textContent = `
    .verse.highlighted {
        background: rgba(255, 215, 0, 0.1);
        border-right: 4px solid var(--islamic-gold);
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    .night-mode {
        --primary-bg: #000000;
        --secondary-bg: #111111;
        --text-primary: #ffffff;
    }
`;
document.head.appendChild(style);
